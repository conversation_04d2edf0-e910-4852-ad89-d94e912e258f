/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./app/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      colors: {
        primary1: "#BE0068",
        primary2: "#E5006B",
        secondary1: "#F08300",
        secondary2: "#E84621",
        tertiary1: "#CABA9F",
        tertiary2: "#6F6F6E",
        tertiary3: "#c8b7bd",
        light: {
          primary: "#E9E8ED",
          secondary: "#838795",
        },
        dark: {
          primary: "#2B2E3B",
          secondary: "#4F5761",
        },
        white: "#ffffff",
      },
      fontSize: {
        xs: "8px",
        sm: "10px",
        md: "12px",
        lg: "14px",
        xl: "16px",
        "2xl": "18px",
        "3xl": "20px",
        "4xl": "24px",
        "5xl": "28px",
        "6xl": "32px",
        "7xl": "36px",
      },
      spacing: {
        "2xs": "10px",
        xs: "20px",
        sm: "30px",
        md: "40px",
        lg: "80px",
        xl: "160px",
      },
      borderRadius: {
        xs: "10px",
        sm: "20px",
        full: "1000px",
      },
    },
    plugins: [],
  },
};
