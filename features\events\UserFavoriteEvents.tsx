import React, { useLayoutEffect } from "react";
import { View, Text, ScrollView, RefreshControl } from "react-native";
import useGetUserFavoriteEvents from "./useGetUserFavoriteEvents";
import EventsList from "@/components/event/EventsList";
import { windowHeight } from "@/lib/device";

type Props = {
  userId: string;
};

const UserFavoriteEvents = ({ userId }: Props) => {
  const { favoriteEvents, isLoading, isFetching, handleRefresh } =
    useGetUserFavoriteEvents({
      userId,
    });

  useLayoutEffect(() => {
    handleRefresh();
  }, []);

  return (
    <ScrollView
      style={{
        overflow: "hidden",
        height: windowHeight / 1.55,
      }}
      refreshControl={
        <RefreshControl refreshing={!!isFetching} onRefresh={handleRefresh} />
      }
    >
      <EventsList
        events={favoriteEvents}
        isLoading={isLoading}
        isFetching={isFetching}
        onRefresh={handleRefresh}
      />
    </ScrollView>
  );
};

export default UserFavoriteEvents;
