import Gap from "@/components/Gap";
import LabelTag from "@/components/LabelTag";
import { RenderIf } from "@/components/RenderIf";
import { MyEventType } from "@/features/events/model";
import { formatDateTime } from "@/lib/format";
import globalStyles from "@/lib/globalStyles";
import { Link, router } from "expo-router";
import React from "react";
import { useTranslation } from "react-i18next";
import {
  ImageBackground,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";

type Props = {
  event: MyEventType;
  style?: ViewStyle;
  imageStyle?: ViewStyle;
  noData?: boolean;
};

const EventCard = ({ event, style, imageStyle: ImageStyle, noData }: Props) => {
  const { t } = useTranslation();
  const showLabels = !!event.highlightedUntil || !!event.sponsoredUntil;

  const media = event.medias?.[0];

  const imageUrl = media?.urlSmall ? media.urlSmall : media?.url;

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={() =>
        router.push({ pathname: "/events/[id]", params: { id: event._id } })
      }
    >
      <View
        style={{
          backgroundColor: globalStyles.colors.light.primary,
          borderRadius: globalStyles.rounded.xs,
        }}
      >
        <ImageBackground
          resizeMode="cover"
          style={[styles.image, ImageStyle]}
          borderRadius={globalStyles.rounded.xs}
          source={
            imageUrl && imageUrl !== null
              ? { uri: imageUrl }
              : require("@/assets/images/event-placeholder.jpg")
          }
        />
      </View>
      <RenderIf isTrue={!noData}>
        <View style={styles.detailContainer}>
          <View style={styles.titleContainer}>
            <Text numberOfLines={2} style={styles.title}>
              {event.name}
            </Text>
            <RenderIf isTrue={showLabels}>
              <>
                <Gap y={2} />
                <View style={styles.labelsContainer}>
                  {!!event.sponsoredUntil && (
                    <LabelTag
                      text={t("common.sponsored")}
                      style={{
                        backgroundColor: globalStyles.colors.primary2,
                      }}
                    />
                  )}
                  {!!event.highlightedUntil && (
                    <>
                      <Gap x={5} />
                      <LabelTag text={t("common.highlight")} />
                    </>
                  )}
                </View>
                <Gap y={2} />
              </>
            </RenderIf>
          </View>
          <Text style={styles.detailSubTitle}>
            {t("common.by")} {event.organizer}
          </Text>
          <Gap y={globalStyles.gap["2xs"]} />
          <Text numberOfLines={1} style={styles.detailDate}>
            {formatDateTime(event.startAt)}
          </Text>
        </View>
      </RenderIf>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
  },
  image: {
    width: 100,
    aspectRatio: 1,
    backgroundColor: globalStyles.colors.light.primary,
    borderRadius: globalStyles.rounded.sm,
  },
  detailContainer: {
    flex: 1,
    justifyContent: "space-between",
    padding: globalStyles.gap["2xs"],
  },
  titleContainer: {
    alignItems: "flex-start",
  },
  title: {
    fontSize: globalStyles.size.xl,
    color: globalStyles.colors.primary1,
    fontWeight: "bold",
  },
  labelsContainer: {
    flexDirection: "row",
  },
  detailSubTitle: {
    fontSize: globalStyles.size.md,
    color: globalStyles.colors.light.secondary,
    fontWeight: "500",
  },
  detailDate: {
    fontSize: globalStyles.size.sm,
    color: globalStyles.colors.light.secondary,
    fontWeight: "500",
  },
});

export default EventCard;
