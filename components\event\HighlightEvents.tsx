import { MyEventType } from "@/features/events/model";
import useAutoScroll from "@/hooks/useAutoScroll";
import { windowWidth } from "@/lib/device";
import globalStyles from "@/lib/globalStyles";
import { useState, Fragment } from "react";
import { ScrollView, View, ActivityIndicator, Text } from "react-native";
import Gap from "../Gap";
import { RenderIf } from "../RenderIf";
import EventCard from "./EventCard/EventCard";
import { useTranslation } from "react-i18next";

type Props = {
  events: MyEventType[];
  isLoading?: boolean;
};

const SCROLL_WIDTH = windowWidth - 30;

export default function HighlightEvents({ events, isLoading }: Props) {
  const { t } = useTranslation();
  const { handleStopScroll, scrollRef } = useAutoScroll({
    events,
    SCROLL_WIDTH,
  });

  const [currPagination, setCurrentPage] = useState(0);
  const paginationCount = Math.ceil(events.length / 3);

  return (
    <>
      <ScrollView
        ref={scrollRef}
        horizontal
        decelerationRate="normal"
        snapToInterval={SCROLL_WIDTH}
        onScrollBeginDrag={handleStopScroll}
        snapToAlignment="start"
        fadingEdgeLength={20}
        onMomentumScrollEnd={(e) => {
          setCurrentPage(
            Math.ceil(e.nativeEvent.contentOffset.x / windowWidth)
          );
        }}
      >
        <RenderIf isTrue={events.length > 0}>
          <>
            {events.map((e) => (
              <Fragment key={e._id}>
                <EventCard
                  noData
                  event={e}
                  imageStyle={{
                    width: (windowWidth - 60) / 3,
                    // height: (windowWidth - 60) / 3,
                  }}
                />
                <Gap x={10} />
              </Fragment>
            ))}
          </>
        </RenderIf>
      </ScrollView>
      <RenderIf isTrue={events.length === 0 && !isLoading}>
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
            alignContent: "center",
            width: "100%",
            gap: globalStyles.gap.xs,
          }}
        >
          <Text>{t("event.no_highlight_events")}</Text>
          <RenderIf isTrue={isLoading}>
            <ActivityIndicator
              size="large"
              color={globalStyles.colors.primary2}
            />
          </RenderIf>
        </View>
      </RenderIf>

      <View
        style={{
          marginVertical: globalStyles.gap["2xs"],
          flexDirection: "row",
          justifyContent: "center",
          width: "100%",
        }}
      >
        {new Array(paginationCount).fill(1).map((_, i) => (
          <View
            key={i}
            style={{
              width: 6,
              height: 6,
              marginHorizontal: 2,
              backgroundColor:
                currPagination === i
                  ? globalStyles.colors.primary1
                  : globalStyles.colors.light.secondary,
              borderRadius: 1000,
            }}
          />
        ))}
      </View>
    </>
  );
}
