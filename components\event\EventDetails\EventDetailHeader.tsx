import { View, Text, StyleSheet } from "react-native";
import React from "react";

import { Octicons } from "@expo/vector-icons";
import globalStyles from "@/lib/globalStyles";
import Gap from "@/components/Gap";
import { MyEventType } from "@/features/events/model";

const EventDetailHeader = ({ event }: { event: MyEventType }) => {
  return (
    <View
      style={{
        flexDirection: "column",
        gap: globalStyles.gap["2xs"],
      }}
    >
      <Text style={styles.title}>{event.name}</Text>
      <View style={styles.promotorContainer}>
        <View style={styles.logo}>
          <Octicons
            name="organization"
            size={10}
            color={globalStyles.colors.light.primary}
          />
        </View>
        <Text style={styles.subtitle}>{event.organizer}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  promotorContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: globalStyles.gap["2xs"],
  },
  title: {
    fontSize: globalStyles.size["3xl"],
    fontWeight: "bold",
    color: globalStyles.colors.dark.primary,
    maxWidth: "80%",
  },
  subtitle: {
    fontSize: globalStyles.size.lg,
  },
  logo: {
    width: 20,
    height: 20,
    borderRadius: 50,
    backgroundColor: globalStyles.colors.light.secondary,
    alignItems: "center",
    justifyContent: "center",
  },
});

export default EventDetailHeader;
