import React from "react";
import { StyleProp, TouchableOpacity, ViewStyle } from "react-native";
import { ImageBackground, Text, View } from "react-native";
import { ITEM_WIDTH } from "./EventSponsoredCard";
import * as Linking from "expo-linking";
import { cn } from "@/lib/utils";
import globalStyles from "@/lib/globalStyles";
import { useTranslation } from "react-i18next";

const EventAdCard = ({ style }: { style?: StyleProp<ViewStyle> }) => {
  const { t } = useTranslation();

  return (
    <TouchableOpacity onPress={() => Linking.openURL("https://zimbora.ao")}>
      <ImageBackground
        resizeMode="cover"
        style={[
          {
            position: "relative",
            flexDirection: "row",
            alignItems: "center",
            padding: globalStyles.gap.xs,
            borderRadius: globalStyles.rounded.sm,
            backgroundColor: globalStyles.colors.light.primary,
            overflow: "hidden",
            aspectRatio: 16 / 9,
            width: ITEM_WIDTH,
          },
          style,
        ]}
        source={require("@/assets/images/ads.jpg")}
      >
        <View
          style={{
            position: "absolute",
            inset: 0,
            backgroundColor: "rgba(0,0,0,0.6)",
          }}
        />
        <Text
          style={{
            fontSize: globalStyles.size["6xl"],
            fontWeight: "bold",
            color: globalStyles.colors.white,
          }}
        >
          {t("event.advertise_here")}
        </Text>
      </ImageBackground>
    </TouchableOpacity>
  );
};

export default EventAdCard;
