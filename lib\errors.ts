import { Alert } from "react-native";
import { ZodError } from "zod";

export const handleErrors = ({
  error,
  message,
}: {
  error: any;
  message?: string;
}): string => {
  if (error instanceof ZodError || "issues" in error) {
    return error.errors?.map((err: any) => err.message).toString();
  }

  return "Ocorreu um erro, tente novamente.";
};

export const createHandleErrorDialog = ({
  title,
  btnText,
  message,
  error,
}: {
  error: Error;
  message?: string;
  title: string;
  btnText?: string;
}) =>
  Alert.alert(title, handleErrors({ error, message }), [
    {
      text: btnText || "OK",
    },
  ]);
