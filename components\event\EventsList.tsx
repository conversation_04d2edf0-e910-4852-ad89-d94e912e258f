import { FlashList } from "@shopify/flash-list";
import React, { Fragment } from "react";
import { ActivityIndicator, RefreshControl, Text, View } from "react-native";
import EventCard from "./EventCard/EventCard";
import { MyEventType } from "@/features/events/model";
import globalStyles from "@/lib/globalStyles";
import Gap from "../Gap";
import { RenderIf } from "../RenderIf";
import { useTranslation } from "react-i18next";

type Props = {
  events: MyEventType[];
  isLoading?: boolean;
  isFetching?: boolean;
  onRefresh?: () => void;
  noScroll?: boolean;
};

export default function EventsList({
  events,
  isLoading,
  isFetching,
  onRefresh,
  noScroll = true,
}: Props) {
  const { t } = useTranslation();

  return (
    <View
      style={{
        height: noScroll ? undefined : "100%",
      }}
    >
      <RenderIf isTrue={events.length > 0}>
        <>
          {noScroll ? (
            events.map((e) => (
              <Fragment key={e._id}>
                <EventCard event={e} />
                <Gap y={globalStyles.gap.xs} />
              </Fragment>
            ))
          ) : (
            <FlashList
              data={events}
              refreshing={isFetching || isLoading}
              renderItem={({ item }) => <EventCard event={item} />}
              estimatedItemSize={200}
              ItemSeparatorComponent={() => <Gap y={globalStyles.gap.xs} />}
              contentContainerStyle={{ paddingBottom: 150 }}
              refreshControl={
                onRefresh ? (
                  <RefreshControl
                    refreshing={isFetching || isLoading || false}
                    onRefresh={onRefresh}
                  />
                ) : undefined
              }
            />
          )}
        </>
      </RenderIf>
      <RenderIf isTrue={events.length === 0 && !isFetching}>
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Text>{t("event.no_events_available")}</Text>
        </View>
      </RenderIf>
      <RenderIf isTrue={isLoading || isFetching}>
        <ActivityIndicator size="large" color={globalStyles.colors.primary2} />
      </RenderIf>
    </View>
  );
}
