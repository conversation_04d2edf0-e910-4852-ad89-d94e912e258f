import React, { useState } from "react";
import {
  Al<PERSON>,
  Share,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Button from "@/components/Button";
import Gap from "@/components/Gap";
import Layout from "@/components/Layout";
import EventDetailHeader from "@/components/event/EventDetails/EventDetailHeader";
import EventDetailImage from "@/components/event/EventDetails/EventDetailImage";
import EventDetailsTimeline from "@/components/event/EventDetails/EventDetailsTimeline";
import globalStyles from "@/lib/globalStyles";
import { Feather } from "@expo/vector-icons";
import EventPriceDetails from "@/components/event/EventPriceDetails";
import * as Linking from "expo-linking";
import Loading from "@/components/Loading";
import { useLocalSearchParams, router } from "expo-router";
import useGetEventById from "@/features/events/useGetEventById";
import { SITE_URL } from "@/config/api";
import CheckoutModal from "@/components/event/CheckoutModal";
import ActivitiesModal from "@/components/event/ActivitiesModal";
import { useTranslation } from "react-i18next";

const openMaps = (location: string) => {
  Linking.openURL(
    `https://www.google.com/maps/search/?api=1&query=${location}`
  );
};

const EventDetailsScreen = () => {
  const { t } = useTranslation();

  const { id } = useLocalSearchParams();
  const [isCheckoutModalOpen, setIsCheckoutModalOpen] = useState(false);
  const [isActivitiesModalOpen, setIsActivitiesModalOpen] = useState(false);

  if (!id || typeof id !== "string") {
    router.replace("/");
    throw new Error("Invalid event id");
  }

  const { event, isLoading } = useGetEventById({ id });

  if (!event && !isLoading) {
    router.replace("/");
    return null;
  }

  if (isLoading || !event) {
    return <Loading />;
  }

  const isEventInFuture = new Date(event.startAt) >= new Date();

  const onShare = async () => {
    try {
      const result = await Share.share({
        message: `${SITE_URL}/events/${event._id}`,
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
        } else {
          // shared
        }
      } else if (result.action === Share.dismissedAction) {
        // dismissed
      }
    } catch (error: any) {
      Alert.alert(error.message);
    }
  };

  return (
    <>
      <Layout headless={false}>
        <View
          style={{
            padding: globalStyles.gap["2xs"],
            paddingTop: 0,
          }}
        >
          <EventDetailHeader event={event} />
          <Gap y={globalStyles.gap.xs} />
          <EventDetailImage
            event={event}
            onShare={onShare}
            onShowActivities={() => setIsActivitiesModalOpen(true)}
            hasActivities={!!(event.activities && event.activities.length > 0)}
          />
          <Gap y={globalStyles.gap.xs} />
          <View
            style={{
              flexDirection: "row",
              gap: globalStyles.gap["2xs"],
            }}
          >
            <Feather
              name="align-left"
              size={18}
              color={globalStyles.colors.dark.secondary}
            />
            <Text style={styles.description}>{event.description}</Text>
          </View>
          <Gap y={globalStyles.gap["xs"]} />
          <View
            style={{
              flexDirection: "row",
            }}
          >
            <Feather
              name="map-pin"
              size={18}
              color={globalStyles.colors.dark.secondary}
            />
            <Gap x={globalStyles.gap["2xs"]} />
            <TouchableOpacity onPress={() => openMaps(event.location)}>
              <Text className="text-lg text-primary1">{event.location}</Text>
            </TouchableOpacity>
          </View>
          <Gap y={globalStyles.gap["xs"]} />
          <EventPriceDetails event={event} />
          <Gap y={globalStyles.gap.xs} />
          <EventDetailsTimeline event={event} />
          <Gap y={globalStyles.gap.lg} />
        </View>
      </Layout>

      {event.checkoutMethods &&
        event.checkoutMethods.length > 0 &&
        isEventInFuture && (
          <>
            <Button
              text={t("common.participate")}
              style={styles.participateBtn}
              onPress={() => setIsCheckoutModalOpen(true)}
            />
            <CheckoutModal
              event={event}
              checkouts={event.checkoutMethods}
              isOpen={isCheckoutModalOpen}
              onClose={() => setIsCheckoutModalOpen(false)}
            />
          </>
        )}
      {event.activities && event.activities.length > 0 && (
        <ActivitiesModal
          eventName={event.name}
          activities={event.activities}
          isOpen={isActivitiesModalOpen}
          onClose={() => setIsActivitiesModalOpen(false)}
        />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: globalStyles.size["2xl"],
    fontWeight: "bold",
  },
  description: {
    fontSize: globalStyles.size.lg,
    width: "93%",
    color: globalStyles.colors.tertiary2,
  },
  participateBtn: {
    position: "absolute",
    width: `${100 - globalStyles.gap["2xs"]}%`,
    bottom: 30,
    alignSelf: "center",
  },
});

export default EventDetailsScreen;
