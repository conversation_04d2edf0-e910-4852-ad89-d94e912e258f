import { useCallback, useRef } from "react";
import { ActivityIndicator, ScrollView, View } from "react-native";
import { useFocusEffect, usePathname } from "expo-router";

import EventAdCard from "./EventAdCard";
import EventSponsoredCard, { ITEM_WIDTH } from "./EventSponsoredCard";
import { MyEventType } from "@/features/events/model";
import { RenderIf } from "../RenderIf";
import globalStyles from "@/lib/globalStyles";

const EventSponsoredList = ({
  events,
  isLoading,
}: {
  events: MyEventType[];
  isLoading: boolean;
}) => {
  const event = events[0];
  return (
    <>
      <RenderIf isTrue={!isLoading}>
        {event ? <EventSponsoredCard event={event} /> : <EventAdCard />}
      </RenderIf>

      <RenderIf isTrue={isLoading}>
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <ActivityIndicator
            size="large"
            color={globalStyles.colors.primary2}
          />
        </View>
      </RenderIf>
    </>
  );
};

export default EventSponsoredList;
