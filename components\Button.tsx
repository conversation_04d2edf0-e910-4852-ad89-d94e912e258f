import globalStyles from "@/lib/globalStyles";
import React from "react";
import {
  ActivityIndicator,
  Pressable,
  StyleProp,
  StyleSheet,
  Text,
  ViewStyle,
} from "react-native";

type Props = {
  children?: JSX.Element;
  text?: string;
  onPress?: () => void;
  style?: StyleProp<ViewStyle>;
  type?: "primary" | "secondary" | "outline" | "link";
  isLoading?: boolean;
  size?: "sm" | "md";
  activeOpacity?: number;
  disabled?: boolean;
};

const Button = ({
  children,
  text,
  onPress,
  style,
  type = "primary",
  size = "md",
  isLoading = false,
  activeOpacity = 0.2,
  disabled = false,
}: Props) => {
  const theme = {
    primary: buttonStyles.primaryButton,
    secondary: buttonStyles.secondaryButton,
    link: buttonStyles.linkButton,
    outline: buttonStyles.outlineButton,
  }[type];

  const textTheme = {
    primary: buttonStyles.primaryText,
    secondary: buttonStyles.secondaryText,
    link: buttonStyles.linkText,
    outline: buttonStyles.outlineText,
  }[type];

  const sizeTheme =
    size === "md" ? buttonStyles.mediumButton : buttonStyles.smallButton;

  const textSizeTheme =
    size === "md" ? buttonStyles.mediumText : buttonStyles.smallText;

  const disabledStyle =
    disabled || isLoading ? buttonStyles.disabledButton : {};

  return (
    <Pressable
      onPress={onPress}
      style={[buttonStyles.button, theme, sizeTheme, disabledStyle, style]}
      disabled={disabled || isLoading}
    >
      {isLoading ? (
        <ActivityIndicator size="small" color={globalStyles.colors.white} />
      ) : (
        children || (
          <Text style={[buttonStyles.text, textTheme, textSizeTheme]}>
            {text}
          </Text>
        )
      )}
    </Pressable>
  );
};

export const buttonStyles = StyleSheet.create({
  button: {
    borderRadius: globalStyles.rounded.full,
    alignItems: "center",
  },
  primaryButton: {
    backgroundColor: globalStyles.colors.primary1,
  },
  secondaryButton: {
    backgroundColor: globalStyles.colors.light.primary,
  },
  outlineButton: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: globalStyles.colors.primary1,
  },
  linkButton: {
    backgroundColor: "transparent",
  },
  disabledButton: {
    backgroundColor: globalStyles.colors.light.secondary,
    opacity: 0.6,
  },
  mediumButton: {
    paddingVertical: globalStyles.gap["2xs"],
    paddingHorizontal: globalStyles.gap.xs,
  },
  smallButton: {
    paddingVertical: 5,
    paddingHorizontal: globalStyles.gap["2xs"],
  },
  text: {
    fontWeight: "500",
    marginTop: -2,
  },
  primaryText: {
    color: globalStyles.colors.white,
  },
  secondaryText: {
    color: globalStyles.colors.primary1,
  },
  outlineText: {
    color: globalStyles.rgba().primary1,
  },
  linkText: {
    color: globalStyles.rgba().primary1,
  },
  mediumText: {
    fontSize: globalStyles.size.lg,
  },
  smallText: {
    fontSize: globalStyles.size.sm,
  },
});

export default Button;
